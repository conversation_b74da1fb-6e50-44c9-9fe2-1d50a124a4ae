import React from 'react';

interface StatsCardProps {
  title: string;
  value: number | string;
  icon: string;
  color: string;
  bgColor: string;
  textColor: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  icon,
  color,
  bgColor,
  textColor,
  trend
}) => {
  return (
    <div className={`${bgColor} rounded-xl p-6 border border-gray-100 hover:shadow-lg transition-shadow duration-200`}>
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
          <p className={`text-3xl font-bold ${textColor} mb-2`}>{value}</p>
          {trend && (
            <div className="flex items-center">
              <i className={`ri-arrow-${trend.isPositive ? 'up' : 'down'}-line text-sm ${
                trend.isPositive ? 'text-green-600' : 'text-red-600'
              } ml-1`}></i>
              <span className={`text-sm font-medium ${
                trend.isPositive ? 'text-green-600' : 'text-red-600'
              }`}>
                {Math.abs(trend.value)}%
              </span>
              <span className="text-sm text-gray-500 mr-1">من الشهر الماضي</span>
            </div>
          )}
        </div>
        <div className={`w-12 h-12 ${color} rounded-lg flex items-center justify-center flex-shrink-0`}>
          <i className={`${icon} text-xl text-white`}></i>
        </div>
      </div>
    </div>
  );
};

export default StatsCard;
