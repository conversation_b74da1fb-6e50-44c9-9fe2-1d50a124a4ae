import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';

const AdminLogin = () => {
  const router = useRouter();
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  });
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [mounted, setMounted] = useState(false);

  // دالة للتحقق من تسجيل الدخول
  const checkAuth = () => {
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('authToken') ||
                   document.cookie.split('; ').find(row => row.startsWith('authToken='))?.split('=')[1];
      return !!token;
    }
    return false;
  };

  useEffect(() => {
    setMounted(true);
    // إذا كان المستخدم مسجل دخول بالفعل، توجيهه إلى لوحة التحكم
    if (checkAuth()) {
      router.push('/admin/dashboard');
    }
  }, [router]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const response = await fetch('/api/auth/admin-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: formData.username,
          password: formData.password
        }),
        credentials: 'include'
      });

      const data = await response.json();

      if (data.success) {
        // حفظ التوكن في التخزين المحلي
        if (data.token) {
          localStorage.setItem('authToken', data.token);
        }

        // توجيه إلى لوحة التحكم
        router.push('/admin/dashboard');
      } else {
        setError(data.messageAr || data.message || 'اسم المستخدم أو كلمة المرور غير صحيحة');
      }
    } catch (error) {
      console.error('Login error:', error);
      setError('حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
    // إزالة رسالة الخطأ عند الكتابة
    if (error) setError('');
  };

  if (!mounted) {
    return null;
  }

  return (
    <>
      <Head>
        <title>تسجيل الدخول - لوحة التحكم</title>
        <meta name="description" content="تسجيل الدخول إلى لوحة التحكم" />
      </Head>

      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-gray-100 to-gray-200 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 relative overflow-hidden" dir="rtl">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-primary to-secondary"></div>
        </div>

        <div className="max-w-md w-full space-y-8 relative z-10">
          {/* Header */}
          <div className="text-center">
            <div className="mx-auto h-20 w-20 bg-gradient-to-r from-primary to-secondary rounded-2xl flex items-center justify-center mb-6 shadow-2xl transform rotate-3 hover:rotate-0 transition-transform duration-300">
              <i className="ri-shield-user-line text-3xl text-white"></i>
            </div>
            <h2 className="text-4xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent mb-2">
              لوحة التحكم
            </h2>
            <p className="text-gray-600 text-lg">
              قم بتسجيل الدخول للوصول إلى لوحة التحكم
            </p>
          </div>

          {/* Login Form */}
       

          {/* Footer */}
          <div className="text-center">
            <p className="text-sm text-gray-500">
              © 2025 DROOB HAJER . جميع الحقوق محفوظة.
            </p>
          </div>
        </div>
      </div>
    </>
  );
};

export default AdminLogin;
